// ========================================
// EXPORT MANAGEMENT SYSTEM
// ========================================

class ExportManager {
    constructor() {
        this.isExporting = false;
        this.exportQuality = 'standard';
        this.exportFormat = 'png';
        
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Export button
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportBillboard();
            });
        }

        // Proceed to payment button
        const proceedPaymentBtn = document.getElementById('proceedPaymentBtn');
        if (proceedPaymentBtn) {
            proceedPaymentBtn.addEventListener('click', () => {
                this.proceedToPayment();
            });
        }
    }

    // Export billboard as image
    async exportBillboard() {
        if (this.isExporting) {
            return;
        }

        const canvas = window.canvasManager.getCanvas();
        if (!canvas) {
            alert('No billboard to export. Please select a template first.');
            return;
        }

        try {
            this.isExporting = true;
            this.showExportProgress('Preparing export...');

            // Get export configuration
            const quality = this.getExportQuality();
            const format = this.getExportFormat();

            // Prepare off-screen clone for export (original canvas unchanged)
            const exportCanvas = await this.prepareCanvasForExport(canvas);

            // Configure export options based on quality
            const options = this.getExportOptions(quality, format);

            // Debug logging for export quality verification
            console.log('Export Configuration:', {
                quality: quality,
                format: format,
                pixelRatio: options.pixelRatio,
                dimensions: `${options.width}x${options.height}`,
                canvasDimensions: `${options.canvasWidth}x${options.canvasHeight}`,
                originalCanvasSize: `${canvas.offsetWidth}x${canvas.offsetHeight}`,
                exportCanvasSize: `${exportCanvas.offsetWidth}x${exportCanvas.offsetHeight}`,
                jpegQuality: options.quality
            });

            this.showExportProgress('Capturing high-quality image...');

            // Export the CLONE (not the original canvas) with error handling
            let dataUrl;
            try {
                if (format === 'jpeg') {
                    dataUrl = await htmlToImage.toJpeg(exportCanvas, options);
                } else {
                    dataUrl = await htmlToImage.toPng(exportCanvas, options);
                }
            } catch (corsError) {
                console.warn('CORS error encountered, trying with simplified options:', corsError);

                // Fallback options with minimal font processing
                const fallbackOptions = {
                    ...options,
                    fontEmbedCSS: '',           // Empty string instead of null
                    skipFonts: true,            // Skip all font processing
                    useCORS: false,             // Disable CORS completely
                    allowTaint: false,          // Prevent tainted canvas
                    ignoreElements: (element) => {
                        // Skip problematic elements
                        return element.tagName === 'LINK' && element.rel === 'stylesheet';
                    }
                };

                // Retry with fallback options
                if (format === 'jpeg') {
                    dataUrl = await htmlToImage.toJpeg(exportCanvas, fallbackOptions);
                } else {
                    dataUrl = await htmlToImage.toPng(exportCanvas, fallbackOptions);
                }
            }

            // Download the image
            this.downloadImage(dataUrl, format);

            this.showExportProgress('Export completed!');
            setTimeout(() => this.hideExportProgress(), 2000);

        } catch (error) {
            console.error('Export failed:', error);
            alert('Export failed. Please try again.');
            this.hideExportProgress();
        } finally {
            this.isExporting = false;
            this.restoreCanvasAfterExport();
        }
    }

    // Get export quality setting
    getExportQuality() {
        // Always use HIGH quality for best results on both desktop and mobile
        // This ensures consistent quality regardless of device
        return 'high'; // Ultra high quality (4x pixel ratio, 98% JPEG quality)
    }

    // Get export format setting
    getExportFormat() {
        // For now, return PNG
        // This could be extended to read from UI controls
        return 'png';
    }

    // Get export options based on quality and format - HIGH QUALITY SYSTEM
    getExportOptions(quality, format) {
        // Use FIXED base dimensions for consistent high quality (like custom billboard)
        const baseWidth = 800;  // Original template design width
        const baseHeight = 400; // Original template design height

        // Get device pixel ratio but ensure minimum quality
        const devicePixelRatio = window.devicePixelRatio || 1;

        // Configure quality settings with consistent high-quality output
        let pixelRatio, canvasMultiplier, jpegQuality;

        switch (quality) {
            case 'high':
                pixelRatio = Math.max(4, devicePixelRatio); // Ultra high quality
                canvasMultiplier = 4;
                jpegQuality = 0.98;
                break;
            case 'web':
                pixelRatio = Math.max(2, devicePixelRatio); // Good quality for web
                canvasMultiplier = 2;
                jpegQuality = 0.85;
                break;
            default: // standard
                pixelRatio = Math.max(3, devicePixelRatio); // High quality for print
                canvasMultiplier = 3;
                jpegQuality = 0.95;
        }

        const baseOptions = {
            quality: jpegQuality,
            pixelRatio: pixelRatio,
            backgroundColor: format === 'png' ? 'transparent' : '#ffffff',
            cacheBust: true,

            // Use FIXED base dimensions regardless of current canvas size (KEY FOR QUALITY)
            width: baseWidth,
            height: baseHeight,
            canvasWidth: baseWidth * canvasMultiplier,
            canvasHeight: baseHeight * canvasMultiplier,

            // Force original dimensions and disable responsive scaling during export
            style: {
                position: 'relative',
                overflow: 'visible',
                transform: 'none',
                transformOrigin: 'top left',
                width: baseWidth + 'px',
                height: baseHeight + 'px',
                maxWidth: 'none',
                minWidth: 'none',
                minHeight: 'none',
                contain: 'none',
                boxSizing: 'border-box',
                margin: '0',
                padding: '0'
            },

            // Filter out UI elements
            filter: (node) => {
                if (node.classList) {
                    return !node.classList.contains('text-customize-btn') &&
                           !node.classList.contains('selected') &&
                           !node.hasAttribute('data-ui-element');
                }
                return true;
            },

            // CORS and Font Handling - FIX FOR GOOGLE FONTS ERRORS
            fontEmbedCSS: null,              // Skip font embedding to avoid CORS errors
            skipFonts: true,                 // Skip font processing entirely
            includeQueryParams: false,
            skipAutoScale: false,
            useCORS: false,                  // Disable CORS to prevent blocking

            // Additional CORS prevention options
            allowTaint: false,               // Don't allow tainted canvas
            foreignObjectRendering: true     // Use foreignObject for better compatibility
        };

        return baseOptions;
    }

    // Get pixel ratio based on quality
    getPixelRatio(quality) {
        switch (quality) {
            case 'web':
                return 2;  // Good quality for web
            case 'standard':
                return 3;  // High quality for print
            case 'high':
                return 4;  // Ultra high quality
            default:
                return 3;
        }
    }

    // Prepare canvas for export - OFF-SCREEN CLONE METHOD (NO LAYOUT SHIFT)
    async prepareCanvasForExport(canvas) {
        // Create an off-screen clone of the canvas to avoid visual disruption
        this.exportClone = canvas.cloneNode(true);

        // Position the clone off-screen (invisible to user)
        this.exportClone.style.position = 'absolute';
        this.exportClone.style.left = '-9999px';
        this.exportClone.style.top = '-9999px';
        this.exportClone.style.visibility = 'hidden';
        this.exportClone.style.pointerEvents = 'none';

        // Set export dimensions on the CLONE only (original canvas unchanged)
        const baseWidth = 800;  // Original template design width
        const baseHeight = 400; // Original template design height

        this.exportClone.style.width = baseWidth + 'px';
        this.exportClone.style.height = baseHeight + 'px';
        this.exportClone.style.maxWidth = 'none';
        this.exportClone.style.minWidth = 'none';
        this.exportClone.style.minHeight = 'none';
        this.exportClone.style.transform = 'none';
        this.exportClone.style.contain = 'none';
        this.exportClone.style.overflow = 'visible';
        this.exportClone.style.aspectRatio = 'unset';

        // Set export flag on clone
        this.exportClone.setAttribute('data-exporting', 'true');

        // Remove UI elements from clone
        const uiElements = this.exportClone.querySelectorAll('.text-customize-btn, .selected');
        uiElements.forEach(el => el.remove());

        // Add clone to document (off-screen)
        document.body.appendChild(this.exportClone);

        // FONT CORS FIX: Apply fallback fonts to prevent Google Fonts CORS errors
        this.applyFallbackFonts(this.exportClone);

        // Ensure all images in clone are loaded
        const images = this.exportClone.querySelectorAll('img');
        const imagePromises = Array.from(images).map(img => {
            return new Promise((resolve) => {
                if (img.complete) {
                    resolve();
                } else {
                    img.onload = resolve;
                    img.onerror = resolve;
                }
            });
        });

        await Promise.all(imagePromises);

        // Small delay to ensure clone rendering is complete
        await new Promise(resolve => setTimeout(resolve, 200));

        return this.exportClone; // Return clone for export
    }

    // Restore canvas after export - OFF-SCREEN CLONE METHOD
    restoreCanvasAfterExport() {
        // Remove the off-screen clone (original canvas was never modified)
        if (this.exportClone && this.exportClone.parentNode) {
            this.exportClone.parentNode.removeChild(this.exportClone);
            this.exportClone = null;
        }

        // Original canvas remains completely unchanged - no restoration needed!
        // This prevents any layout shifts or visual disruption
    }

    // Apply fallback fonts to prevent Google Fonts CORS errors
    applyFallbackFonts(element) {
        // Font mapping with safe fallbacks
        const fontFallbacks = {
            'Inter': 'Arial, sans-serif',
            'Roboto': 'Arial, sans-serif',
            'Open Sans': 'Arial, sans-serif',
            'Lato': 'Arial, sans-serif',
            'Montserrat': 'Arial, sans-serif',
            'Poppins': 'Arial, sans-serif',
            'Playfair Display': 'Georgia, serif',
            'Merriweather': 'Georgia, serif',
            'Source Sans Pro': 'Arial, sans-serif',
            'Nunito': 'Arial, sans-serif',
            'Raleway': 'Arial, sans-serif',
            'Oswald': 'Arial, sans-serif',
            'Dancing Script': 'cursive',
            'Pacifico': 'cursive',
            'Lobster': 'cursive'
        };

        // Find all text elements in the clone
        const textElements = element.querySelectorAll('.text-element, [style*="font-family"]');

        textElements.forEach(el => {
            const currentFont = el.style.fontFamily;
            if (currentFont) {
                // Extract the primary font name
                const primaryFont = currentFont.split(',')[0].replace(/['"]/g, '').trim();

                // Apply fallback if it's a Google Font
                if (fontFallbacks[primaryFont]) {
                    el.style.fontFamily = fontFallbacks[primaryFont];
                    console.log(`Font fallback applied: ${primaryFont} -> ${fontFallbacks[primaryFont]}`);
                }
            }
        });
    }

    // Download image
    downloadImage(dataUrl, format) {
        const link = document.createElement('a');
        link.download = `billboard-${Date.now()}.${format}`;
        link.href = dataUrl;
        
        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Show export progress
    showExportProgress(message) {
        // Create or update progress indicator
        let progressEl = document.getElementById('exportProgress');
        if (!progressEl) {
            progressEl = document.createElement('div');
            progressEl.id = 'exportProgress';
            progressEl.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 20px 30px;
                border-radius: 10px;
                z-index: 10000;
                font-size: 16px;
                text-align: center;
            `;
            document.body.appendChild(progressEl);
        }
        
        progressEl.textContent = message;
        progressEl.style.display = 'block';
    }

    // Hide export progress
    hideExportProgress() {
        const progressEl = document.getElementById('exportProgress');
        if (progressEl) {
            progressEl.style.display = 'none';
        }
    }

    // Proceed to payment (placeholder)
    proceedToPayment() {
        // This would integrate with the existing payment system
        alert('Payment integration would be implemented here.\n\nThis would connect to the existing checkout system from the custom billboard maker.');
        
        // For now, just export the billboard
        this.exportBillboard();
    }

    // Check if export is available
    isExportAvailable() {
        const canvas = window.canvasManager.getCanvas();
        const template = window.canvasManager.getCurrentTemplate();
        return canvas && template;
    }

    // Get export status
    getExportStatus() {
        return {
            isExporting: this.isExporting,
            isAvailable: this.isExportAvailable()
        };
    }
}

// Global functions for HTML onclick handlers
function exportBillboard() {
    window.exportManager.exportBillboard();
}

function proceedToPayment() {
    window.exportManager.proceedToPayment();
}

// Create global instance
window.exportManager = new ExportManager();
