<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title>Templated Billboard Designer - Borges Media</title>

    <!-- Google Fonts - Popular and Accessible Font Choices -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Montserrat:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Merriweather:wght@300;400;700&family=Source+Sans+Pro:wght@300;400;600;700&family=Nunito:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&family=Oswald:wght@300;400;500;600;700&family=Dancing+Script:wght@400;500;600;700&family=Pacifico&family=Lobster&display=swap" rel="stylesheet">

    <!-- Font Awesome 6 - For Text Alignment Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Templated Billboard Specific Styles -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        /* Left Panel - Controls */
        .controls-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            height: fit-content;
        }

        .control-section {
            margin-bottom: 25px;
        }

        .control-section h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .category-dropdown {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1rem;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-dropdown:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .template-card {
            background: white;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            padding: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .template-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .template-card.selected {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .template-preview {
            width: 100%;
            height: 60px;
            background: #f1f3f4;
            border-radius: 5px;
            margin-bottom: 8px;
            background-size: cover;
            background-position: center;
        }

        .template-name {
            font-size: 0.85rem;
            font-weight: 500;
            color: #555;
        }

        /* Text Fields Panel */
        .text-fields-panel {
            background: #f0f4ff;
            border: 2px solid #667eea;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            display: none;
        }

        .text-fields-panel.visible {
            display: block;
        }

        .text-fields-panel h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .text-field-group {
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .text-field-label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
            display: block;
        }

        .text-field-input {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .text-field-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .text-customize-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            margin-top: 8px;
            transition: all 0.3s ease;
        }

        .text-customize-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        /* Right Panel - Canvas */
        .canvas-panel {
            display: flex;
            flex-direction: column;
        }

        .canvas-container {
            flex: 1;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 300px;
        }

        .billboard-canvas {
            /* PROPER RESPONSIVE SOLUTION: Fluid width + aspect ratio */
            width: 100%;
            max-width: 800px;
            aspect-ratio: 2 / 1;
            background: white;
            border-radius: 10px;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;

            /* Ensure minimum size for usability */
            min-width: 280px;
            min-height: 140px;

            /* Enable container queries for responsive font sizing */
            container-type: inline-size;
            container-name: billboard-canvas;
        }

        .canvas-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
        }

        .text-element {
            position: absolute;
            cursor: pointer;
            user-select: none;
            transition: all 0.2s ease;
            padding: 2px 4px;
            border-radius: 3px;
            min-width: 20px;
            min-height: 20px;
            line-height: 1.2;

            /* Font size will be set dynamically by JavaScript based on template specs */
        }

        .text-element:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .text-element.selected {
            background: rgba(102, 126, 234, 0.2);
            outline: 2px dashed #667eea;
        }

        /* Touch-friendly improvements for mobile */
        @media (max-width: 768px) {
            .text-element {
                min-width: 30px;
                min-height: 30px;
                padding: 4px 6px;
            }

            .image-element {
                min-width: 40px;
                min-height: 40px;
            }

            /* Larger touch targets */
            .template-card {
                min-height: 80px;
                padding: 15px;
            }

            .text-customize-btn {
                padding: 10px 16px;
                font-size: 1rem;
            }
        }

        .image-element {
            position: absolute;
            cursor: pointer;
            border-radius: 5px;
            overflow: hidden;
            transition: all 0.2s ease;
        }

        .image-element img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-element:hover {
            outline: 2px solid #667eea;
        }

        .image-element.selected {
            outline: 3px solid #667eea;
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
            justify-content: center;
            min-width: 150px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn:disabled:hover {
            transform: none !important;
            background: inherit !important;
        }

        /* Loading state */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-style: italic;
            padding: 20px;
            text-align: center;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .modal.hidden {
            display: none;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
        }

        .modal-header {
            padding: 20px 25px;
            border-bottom: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #667eea;
            color: white;
        }

        .modal-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: white;
            padding: 5px;
            border-radius: 5px;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .modal-body {
            padding: 25px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .background-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
        }

        .background-option {
            aspect-ratio: 2/1;
            background-size: cover;
            background-position: center;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .background-option:hover {
            border-color: #667eea;
            transform: scale(1.05);
        }

        /* Text Customization Panel */
        .customization-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            z-index: 1000;
            width: 400px;
            max-height: 80vh;
            overflow: hidden;
            animation: slideIn 0.3s ease;
        }

        .customization-panel.hidden {
            display: none;
        }

        .panel-header {
            padding: 20px 25px;
            border-bottom: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #667eea;
            color: white;
        }

        .panel-header h3 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .panel-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: white;
            padding: 5px;
            border-radius: 5px;
            transition: all 0.2s ease;
        }

        .panel-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .panel-body {
            padding: 25px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group input[type="range"] {
            padding: 0;
        }

        .form-group input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e1e8ed;
        }

        .form-actions .btn {
            flex: 1;
        }

        /* Responsive Design - Mobile First Approach */

        /* Mobile First (320px+) */
        .main-content {
            grid-template-columns: 1fr;
            gap: 15px;
            padding: 15px;
        }

        .canvas-container {
            min-height: 200px;
            padding: 10px;
        }

        /* Ensure canvas is always visible on mobile */
        .billboard-canvas {
            width: 100%;
            max-width: none;
        }

        .templates-grid {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .action-buttons {
            flex-direction: column;
            gap: 10px;
        }

        .action-buttons .btn {
            width: 100%;
            justify-content: center;
            min-width: auto;
        }

        .text-fields-panel {
            order: -1;
        }

        /* Large Mobile (481px+) */
        @media (min-width: 481px) {
            .templates-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .canvas-container {
                min-height: 250px;
                padding: 15px;
            }
        }

        /* Tablet Portrait (768px+) */
        @media (min-width: 768px) {
            .templates-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .canvas-container {
                min-height: 300px;
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }
        }

        /* Tablet Landscape / Small Desktop (1024px+) */
        @media (min-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr 2fr;
                gap: 30px;
                padding: 30px;
            }

            .canvas-container {
                min-height: 400px;
            }

            .text-fields-panel {
                order: 0;
            }

            .action-buttons {
                flex-direction: row;
            }

            .action-buttons .btn {
                width: auto;
                min-width: 150px;
            }
        }

        /* Large Desktop (1200px+) */
        @media (min-width: 1200px) {
            .container {
                padding: 20px;
            }

            .canvas-container {
                min-height: 500px;
            }
        }



        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -60%);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }


    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-palette"></i> Templated Billboard Maker</h1>
            <p>Choose a template, customize it, and create your perfect billboard</p>
        </div>

        <div class="main-content">
            <!-- Left Panel - Controls -->
            <div class="controls-panel">
                <!-- Category Selection -->
                <div class="control-section">
                    <h3><i class="fas fa-list"></i> Select Category</h3>
                    <select class="category-dropdown" id="categorySelect">
                        <option value="">Choose a category...</option>
                    </select>
                </div>

                <!-- Template Selection -->
                <div class="control-section">
                    <h3><i class="fas fa-images"></i> Choose Template</h3>
                    <div class="templates-grid" id="templatesGrid">
                        <div class="loading">Select a category to view templates</div>
                    </div>
                </div>

                <!-- Text Fields Panel (Hidden initially) -->
                <div class="text-fields-panel" id="textFieldsPanel">
                    <h3><i class="fas fa-font"></i> Edit Text Content</h3>
                    <div id="textFieldsContainer">
                        <!-- Text fields will be populated here -->
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button class="btn btn-secondary" id="changeBackgroundBtn" disabled>
                        <i class="fas fa-image"></i> Change Background
                    </button>
                    <button class="btn btn-secondary" id="uploadImageBtn" disabled>
                        <i class="fas fa-upload"></i> Upload Image
                    </button>
                </div>
            </div>

            <!-- Right Panel - Canvas -->
            <div class="canvas-panel">
                <div class="canvas-container">
                    <div class="billboard-canvas" id="billboardCanvas" data-width="800" data-height="400">
                        <div class="canvas-background" id="canvasBackground"></div>
                        <!-- Text and image elements will be added here dynamically -->
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" id="exportBtn" disabled>
                        <i class="fas fa-download"></i> Export Billboard
                    </button>
                    <button class="btn btn-success" id="proceedPaymentBtn" disabled>
                        <i class="fas fa-credit-card"></i> Proceed to Payment
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->

    <!-- Background Selection Modal -->
    <div id="backgroundModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-image"></i> Choose Background</h3>
                <button class="modal-close" onclick="closeBackgroundModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="background-grid" id="backgroundGrid">
                    <!-- Background options will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Text Customization Panel -->
    <div id="textCustomizationPanel" class="customization-panel hidden">
        <div class="panel-header">
            <h3><i class="fas fa-font"></i> Text Customization</h3>
            <button class="panel-close" onclick="closeTextCustomization()">&times;</button>
        </div>
        <div class="panel-body">
            <div class="form-group">
                <label>Text Content:</label>
                <input type="text" id="textContent" placeholder="Enter text...">
            </div>
            <div class="form-group">
                <label>Font Color:</label>
                <input type="color" id="textColor" value="#ffffff">
            </div>
            <div class="form-group">
                <label>Font Size:</label>
                <input type="range" id="fontSize" min="12" max="72" value="32">
                <span id="fontSizeValue">32px</span>
            </div>
            <div class="form-group">
                <label>Font Family:</label>
                <select id="fontFamily">
                    <option value="Inter">Inter</option>
                    <option value="Dancing Script">Dancing Script</option>
                    <option value="Roboto">Roboto</option>
                    <option value="Open Sans">Open Sans</option>
                    <option value="Lato">Lato</option>
                    <option value="Montserrat">Montserrat</option>
                    <option value="Poppins">Poppins</option>
                    <option value="Playfair Display">Playfair Display</option>
                    <option value="Merriweather">Merriweather</option>
                    <option value="Source Sans Pro">Source Sans Pro</option>
                    <option value="Nunito">Nunito</option>
                    <option value="Raleway">Raleway</option>
                    <option value="Oswald">Oswald</option>
                    <option value="Pacifico">Pacifico</option>
                    <option value="Lobster">Lobster</option>
                </select>
            </div>
            <div class="form-group">
                <label>Font Weight:</label>
                <select id="fontWeight">
                    <option value="300">Light</option>
                    <option value="400">Normal</option>
                    <option value="500">Medium</option>
                    <option value="600">Semi Bold</option>
                    <option value="700">Bold</option>
                    <option value="900">Black</option>
                </select>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="textShadow"> Add Text Shadow
                </label>
            </div>
            <div class="form-actions">
                <button class="btn btn-primary" onclick="applyTextChanges()">Apply Changes</button>
                <button class="btn btn-secondary" onclick="closeTextCustomization()">Cancel</button>
            </div>
        </div>
    </div>

        <!-- File Upload Input (Hidden) -->
        <input type="file" id="imageUpload" accept="image/*" style="display: none;">



    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/html-to-image@1.11.13/dist/html-to-image.js"></script>

    <!-- Template Specifications -->
    <script src="template-specs.js"></script>

    <!-- Main Application Scripts -->
    <script src="assets/js/template-manager.js"></script>
    <script src="assets/js/canvas-manager.js"></script>
    <script src="assets/js/customization-manager.js"></script>
    <script src="assets/js/export-manager.js"></script>
    <script src="assets/js/app.js"></script>

    <script>
        // Global functions for navigation
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '../';
            }
        }
    </script>

</body>
</html>